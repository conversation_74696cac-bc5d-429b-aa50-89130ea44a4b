import {
  Contact,
  EtablissementProfessionnel,
  Evenement,
  horaire_hebdomadaire,
  LangueParleeProfessionnel,
  MotClesProfessionnel,
  Professionnel,
  RendezVous,
  SpecialiteProfessionnel,
} from "../models";
import { AvailabilitySettingsDTO } from "./AvailabililtySettingsDTO";

// Interfaces for transformed data used by the ExpertiseSection component
export interface ProfessionalMotCle {
  id: number;
  mot_cle: string;
  id_professionnel: number;
}

export interface ProfessionalLangue {
  id: number;
  nom_langue: string;
  niveau?: string;
  id_professionnel: number;
}

export type SearchProfessionalDTO = Professionnel & {
  rendez_vous: RendezVous[];
  specialites_professionnel: SpecialiteProfessionnel[];
  evenement: Evenement[];
  etablissements_professionnel: EtablissementProfessionnel[];
  parametre_disponibilite: AvailabilitySettingsDTO[];
  contacts: Contact[];
  mot_cles: MotClesProfessionnel[];
  langues_parlees_professionnel?: LangueParleeProfessionnel[];
};

export type TimeSlotProffessionalCard = {
  date: string;
  start: string;
  end: string;
};

export type ProfessionalCardDTO = Professionnel & {
  specialite: SpecialiteProfessionnel[];
  disponibilite: TimeSlotProffessionalCard[];
  etablissements_professionnel: EtablissementProfessionnel[];
  horaire_hebdomadaire?: horaire_hebdomadaire[];
  contacts: Contact[];
  motCles?: ProfessionalMotCle[];
  langues?: ProfessionalLangue[];
};

export type ProfessionalCompleteDTO = Professionnel & {
  specialites_professionnel: SpecialiteProfessionnel[];
};

export type ProfessionalProfileData = Professionnel & {
  specialites_professionnel: SpecialiteProfessionnel[];
  parametre_disponibilite: AvailabilitySettingsDTO;
  etablissements_professionnel: EtablissementProfessionnel[];
};
